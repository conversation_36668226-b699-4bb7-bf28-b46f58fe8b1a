import {
  getAvatarForRole,
  getRoleClasses,
  getRoleDisplayName,
  isRightAlignedRole,
  shouldShowAvatarForRole,
  shouldShowBoxForRole,
  shouldShowRoleName,
  type ChatRole
} from '@/shared/utils/avatarMapping';
import React, { useEffect, useRef, useState } from 'react';
import { ReplyIndicator } from '../ReplyIndicator';
import { StreamingText } from '../StreamingText';

import { ReplyMessage } from '../ReplyPreview/ReplyPreview';

// Re-export ChatRole for convenience
export type { ChatRole };

export interface ChatMessageProps {
  /**
   * Nội dung tin nhắn
   */
  content: React.ReactNode;

  /**
   * Người gửi tin nhắn - hỗ trợ thêm role supervisor và worker
   */
  sender: ChatRole;

  /**
   * Thời gian gửi tin nhắn
   */
  timestamp: Date;

  /**
   * URL avatar (chỉ hiển thị cho tin nhắn từ AI)
   */
  avatar?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Có đang streaming không (để hiệu ứng typing)
   */
  isStreaming?: boolean;

  /**
   * Có đang chờ tokens mới không
   */
  isWaiting?: boolean;

  /**
   * Role cụ thể cho SSE events (supervisor/worker)
   */
  role?: string;

  /**
   * Callback khi click reply
   */
  onReply?: (message: ReplyMessage) => void;

  /**
   * ID của tin nhắn (để reply)
   */
  messageId?: string;

  /**
   * Callback khi click vào reply indicator để focus message
   */
  onFocusMessage?: (messageId: string) => void;

  /**
   * Có collapsed không (cho worker messages)
   */
  isCollapsed?: boolean;

  /**
   * Callback khi toggle collapse state
   */
  onToggleCollapse?: () => void;

  /**
   * Content preview khi collapsed
   */
  contentPreview?: string;

  /**
   * Message metadata (cho animation và state)
   */
  metadata?: {
    isHiding?: boolean;
    hideAfterDelay?: boolean;
    [key: string]: unknown;
  };

  /**
   * Message status (cho animation)
   */
  messageStatus?: 'streaming' | 'completed' | 'error';

  /**
   * Reply information từ API mới
   */
  replyInfo?: {
    replyToMessageId: string;
    replyContent?: string;
  };

  /**
   * Edit functionality props
   */
  isLastUserMessage?: boolean; // Có phải tin nhắn user cuối cùng không
  onEditMessage?: (content: string) => void; // Callback để edit message
}

/**
 * Component hiển thị tin nhắn trong chat
 */
const ChatMessage: React.FC<ChatMessageProps> = props => {
  const {
    content,
    sender,
    timestamp,
    className = '',
    isStreaming = false,
    isWaiting = false,
    avatar,
    onReply,
    messageId,
    onFocusMessage,
    // isCollapsed = false,
    // onToggleCollapse,
    // contentPreview,
    metadata,
    messageStatus,
    replyInfo,
    isLastUserMessage = false,
    onEditMessage
  } = props;
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const messageRef = useRef<HTMLDivElement>(null);

  console.log('[ChatMessage] Rendering message:', timestamp);

  // Xử lý reply context từ API mới hoặc replyInfo prop
  const hasReply = !!(replyInfo?.replyToMessageId);
  const actualContent = content; // Content đã được xử lý từ API
  const replyContent = replyInfo?.replyContent;
  const replyMessageId = replyInfo?.replyToMessageId;

  // Debug log để kiểm tra reply info
  if (replyInfo?.replyToMessageId) {
    console.log('[ChatMessage] Reply info detected:', {
      messageId,
      hasReply,
      replyToMessageId: replyMessageId,
      replyContent: replyContent?.substring(0, 50) + '...',
      willShowReplyIndicator: hasReply && !!replyContent
    });
  }

  // Animation khi component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50); // Delay nhỏ để animation mượt hơn

    return () => clearTimeout(timer);
  }, []);

  // Format timestamp
  // const formatTime = (date: Date): string => {
  //   return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  // };

  // Worker message animation logic
  const isWorkerMessage = sender === 'worker';
  const isHiding = isWorkerMessage && metadata?.isHiding;
  const isCompleted = isWorkerMessage && messageStatus === 'completed';

  // Animation classes - kết hợp base animation và worker-specific animation
  const baseAnimationClasses = isVisible
    ? 'opacity-100 translate-y-0 scale-100'
    : 'opacity-0 translate-y-2 scale-95';

  const streamingClasses = isStreaming
    ? 'animate-pulse'
    : '';

  // Worker message specific animation classes
  const workerAnimationClasses = isWorkerMessage
    ? isHiding
      ? 'opacity-0 transform scale-95 translate-y-2 transition-all duration-500 ease-in-out'
      : isCompleted
        ? 'opacity-100 transform scale-100 translate-y-0 transition-all duration-300 ease-out'
        : 'opacity-100 transform scale-100 translate-y-0'
    : '';

  // Combine animation classes
  const animationClasses = isWorkerMessage
    ? workerAnimationClasses
    : `${baseAnimationClasses} transform transition-all duration-300 ease-out`;

  // Sử dụng avatar mapping utilities
  const avatarConfig = getAvatarForRole(sender, avatar);
  const avatarUrl = avatar || avatarConfig.url;
  const shouldShowAvatar = shouldShowAvatarForRole(sender);
  const shouldShowBox = shouldShowBoxForRole(sender);
  const isRightAligned = isRightAlignedRole(sender);
  const showRoleName = shouldShowRoleName(sender);
  const roleClasses = getRoleClasses(sender);
  const roleDisplayName = getRoleDisplayName(sender);

  // Handler cho reply
  const handleReply = () => {
    if (onReply && !hasReply) { // Không cho phép reply message đã có reply context
      const replyMessage: ReplyMessage = {
        content: actualContent, // Sử dụng actual content, không bao gồm reply context
        sender,
        timestamp
      };

      // Chỉ thêm id nếu messageId có giá trị
      if (messageId) {
        replyMessage.id = messageId;
      }

      onReply(replyMessage);
    }
  };

  // Handler cho focus message
  const handleFocusMessage = (targetMessageId?: string) => {
    if (onFocusMessage && targetMessageId) {
      onFocusMessage(targetMessageId);
    }
  };

  // Handler cho edit message
  const handleEditMessage = () => {
    if (onEditMessage && typeof content === 'string') {
      onEditMessage(content);
    }
  };

  return (
    <div
      ref={messageRef}
      className={`w-full ${className} group`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      id={messageId ? `message-${messageId}` : undefined}
    >
      {/* Hiển thị role name cho supervisor/worker */}
      {showRoleName && (
        <div className={roleClasses.roleName}>
          {roleDisplayName}
        </div>
      )}

      {/* Hiển thị reply indicator nếu message có reply context */}
      {hasReply && replyContent && (
        <ReplyIndicator
          replyContent={replyContent}
          {...(replyMessageId && { replyMessageId })} // Chỉ pass nếu có giá trị
          onReplyClick={handleFocusMessage}
          className="mb-2"
        />
      )}

      <div
        className={`${roleClasses.container} relative ${animationClasses}`}
      >
        {/* Avatar bên trái cho AI roles */}
        {shouldShowAvatar && !isRightAligned && (
          <div className="flex-shrink-0">
            <div className={roleClasses.avatar}>
              <img
                src={avatarUrl}
                alt={avatarConfig.alt}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        {/* Nội dung tin nhắn với reply button và edit button */}
        <div className={`${roleClasses.content} flex items-center gap-2`}>
          {/* Reply button và Edit button cho user - hiển thị ở đằng trước (bên trái) */}
          {isRightAligned && (
            <div className="flex-shrink-0 flex items-center gap-1">
              {/* Debug - tạm thời để kiểm tra */}
              {sender === 'user' && (
                <div className="text-xs bg-red-100 p-1 rounded">
                  isLast: {isLastUserMessage ? 'YES' : 'NO'} | onEdit: {onEditMessage ? 'YES' : 'NO'}
                </div>
              )}

              {/* Edit button - chỉ hiển thị cho tin nhắn user cuối cùng */}
              {isLastUserMessage && onEditMessage && (
                <button
                  onClick={handleEditMessage}
                  className={`p-1.5 rounded-full bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20
                    text-blue-500 hover:text-blue-600 dark:hover:text-blue-400
                    transition-all duration-200 shadow-md border border-gray-200 dark:border-gray-600
                    ${isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                  title={isLastUserMessage ? "Sửa tin nhắn này" : "Sửa tin nhắn (test)"}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
              )}
              {/* Reply button */}
              {onReply && (
                <button
                  onClick={handleReply}
                  className={`p-1.5 rounded-full bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20
                    text-red-500 hover:text-red-600 dark:hover:text-red-400
                    transition-all duration-200 shadow-md border border-gray-200 dark:border-gray-600
                    ${isHovered && !hasReply ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                  title="Reply to this message"
                  disabled={hasReply}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                      d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                  </svg>
                </button>
              )}
            </div>
          )}

          {/* Message content */}
          <div className="flex-1 min-w-0">
            {shouldShowBox ? (
              // User message với box
              <div
                className={`p-3 rounded-lg break-words overflow-hidden ${streamingClasses}
                  bg-primary text-white rounded-tr-none`}
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto'
                }}
              >
                <div className="whitespace-pre-wrap break-words overflow-hidden">
                  {isStreaming && typeof actualContent === 'string' ? (
                    <StreamingText
                      text={actualContent}
                      isStreaming={isStreaming}
                      isWaiting={isWaiting}
                      showCursor={true}
                      cursorStyle="blink"
                    />
                  ) : (
                    actualContent
                  )}
                </div>
              </div>
            ) : (
              // AI message không có box, hiển thị trực tiếp
              <div
                className={`break-words overflow-hidden ${streamingClasses}
                  text-gray-900 dark:text-gray-100`}
                style={{
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto'
                }}
              >
                <div className="whitespace-pre-wrap break-words overflow-hidden">
                  {isStreaming && typeof actualContent === 'string' ? (
                    <StreamingText
                      text={actualContent}
                      isStreaming={isStreaming}
                      isWaiting={isWaiting}
                      showCursor={true}
                      cursorStyle="blink"
                    />
                  ) : (
                    actualContent
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Reply button cho AI - hiển thị ở đằng sau (bên phải) */}
          {onReply && !isRightAligned && (
            <div className="flex-shrink-0 w-8 flex items-center justify-center">
              <button
                onClick={handleReply}
                className={`p-1.5 rounded-full bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20
                  text-red-500 hover:text-red-600 dark:hover:text-red-400
                  transition-all duration-200 shadow-md border border-gray-200 dark:border-gray-600
                  ${isHovered && !hasReply ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                title="Reply to this message"
                disabled={hasReply}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Avatar bên phải cho user (nếu cần) */}
        {shouldShowAvatar && isRightAligned && (
          <div className="flex-shrink-0">
            <div className={roleClasses.avatar}>
              <img
                src={avatarUrl}
                alt={avatarConfig.alt}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
