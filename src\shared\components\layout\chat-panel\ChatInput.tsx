import { LoadingAnimation, ReplyMessage } from '@/shared/components/common';
import { UseChatStreamReturn } from '@/shared/hooks/common/useChatStream';
import { AuthType, useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useChatInputFocus } from '@/shared/hooks/useGlobalKeyboardShortcuts';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ChatInputBox from './ChatInputBox';
import { adminMenuItems, userMenuItems } from './menu-items';
import ModernMenu from './ModernMenu';

interface ChatInputProps {
  onKeywordDetected?: (keyword: string) => void;
  // Chat streaming props
  chatStream?: UseChatStreamReturn;
  addNotification?: (type: 'success' | 'error' | 'warning' | 'info', message: string, duration?: number) => void;
  // Reply message props
  replyMessage?: ReplyMessage | null;
  onClearReply?: () => void;
  // Center notification props
  setCenterNotification?: (notification: { message: string; type?: 'info' | 'success' | 'warning' | 'error'; duration?: number; } | null) => void;
  // Edit message props
  editContent?: string;
  onEditComplete?: () => void;
}

const ChatInput = ({ onKeywordDetected, chatStream, addNotification, replyMessage: externalReplyMessage, onClearReply: externalOnClearReply, setCenterNotification, editContent, onEditComplete }: ChatInputProps) => {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [chatInputWidth, setChatInputWidth] = useState<number | undefined>(undefined);
  const chatInputRef = useRef<HTMLDivElement>(null);
  const [filterText, setFilterText] = useState<string>('');
  const [isSlashCommand, setIsSlashCommand] = useState<boolean>(false);

  // Use external reply message if provided, otherwise use local state
  const replyMessage = externalReplyMessage;
  const onClearReply = externalOnClearReply;

  // Hook để quản lý phím tắt focus chat input
  const { setChatInputRef } = useChatInputFocus();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Cập nhật ref cho hook phím tắt khi component mount
  useEffect(() => {
    setChatInputRef(textareaRef.current);
  }, [setChatInputRef]);

  // Focus vào input khi có reply message
  useEffect(() => {
    if (replyMessage && textareaRef.current) {
      // Delay nhỏ để đảm bảo DOM đã render
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);
    }
  }, [replyMessage]);

  // Lấy chiều rộng của ô chat input để đồng bộ với menu
  useEffect(() => {
    if (chatInputRef.current) {
      const updateWidth = () => {
        if (chatInputRef.current) {
          // Tìm ChatInputBox container
          const chatInputBox = chatInputRef.current.querySelector('.chat-input-box-container');

          if (chatInputBox) {
            // Lấy width chính xác của ChatInputBox
            const boxWidth = chatInputBox.getBoundingClientRect().width;

            // Đảm bảo width tối thiểu
            const minWidth = 300;
            const adjustedWidth = Math.max(boxWidth, minWidth);

            // Chỉ cập nhật khi thay đổi đáng kể
            if (adjustedWidth > 0 && Math.abs(adjustedWidth - (chatInputWidth || 0)) > 5) {
              setChatInputWidth(adjustedWidth);
            }
          }
        }
      };

      // Cập nhật chiều rộng ban đầu
      updateWidth();

      // Cập nhật chiều rộng khi cửa sổ thay đổi kích thước
      window.addEventListener('resize', updateWidth);

      // Lắng nghe sự kiện layout-resized và layout-resizing từ ResizableLayout
      window.addEventListener('layout-resized', updateWidth);
      window.addEventListener('layout-resizing', updateWidth);

      // Tạo một ResizeObserver để theo dõi thay đổi kích thước của container
      const resizeObserver = new ResizeObserver(() => {
        requestAnimationFrame(updateWidth);
      });
      const currentRefForObserver = chatInputRef.current;
      if (currentRefForObserver) {
        resizeObserver.observe(currentRefForObserver);
      }

      // Cập nhật chiều rộng mỗi 100ms để đảm bảo bắt được thay đổi khi kéo thanh resizeable
      const interval = setInterval(updateWidth, 100);

      // Cập nhật chiều rộng khi menu mở
      if (isMenuOpen) {
        updateWidth();
      }

      return () => {
        window.removeEventListener('resize', updateWidth);
        window.removeEventListener('layout-resized', updateWidth);
        window.removeEventListener('layout-resizing', updateWidth);
        if (currentRefForObserver) {
          resizeObserver.unobserve(currentRefForObserver);
        }
        resizeObserver.disconnect();
        clearInterval(interval);
      };
    }

    return undefined;
  }, [chatInputWidth, isMenuOpen]);

  // Áp dụng translation cho menu items
  const menuItems = useAuthCommon().authType == AuthType.ADMIN ? adminMenuItems : userMenuItems;

  // const keyword = useChatKeywords(useAuthCommon().authType);

  // Kiểm tra từ khóa trong tin nhắn và điều hướng nếu phù hợp
  // const checkKeywordsAndNavigate = (message: string): boolean => {
  //   const lowerMessage = message.toLowerCase().trim();

  //   // Kiểm tra từng menu item
  //   for (const item of keyword) {
  //     // Kiểm tra label
  //     if (item.label.toLowerCase().includes(lowerMessage)) {
  //       // Truyền đường dẫn để điều hướng trực tiếp
  //       onKeywordDetected?.(item.path);
  //       console.log(`Keyword match found in label: "${message.trim()}" -> ${item.path}`);
  //       return true;
  //     }

  //     // Kiểm tra path (bỏ qua path "/")
  //     if (item.path.toLowerCase().includes(lowerMessage) && item.path !== '/') {
  //       // Truyền đường dẫn để điều hướng trực tiếp
  //       onKeywordDetected?.(item.path);
  //       console.log(`Keyword match found in path: "${message.trim()}" -> ${item.path}`);
  //       return true;
  //     }

  //     // Kiểm tra keywords
  //     if (
  //       item.keywords &&
  //       item.keywords.some((keyword: string) => {
  //         const keywordLower = keyword.toLowerCase();
  //         return lowerMessage.includes(keywordLower) || keywordLower.includes(lowerMessage);
  //       })
  //     ) {
  //       // Truyền đường dẫn để điều hướng trực tiếp
  //       onKeywordDetected?.(item.path);
  //       console.log(`Keyword match found in keywords: "${message.trim()}" -> ${item.path}`);
  //       return true;
  //     }
  //   }

  //   return false;
  // };

  // Handle keyword detection (moved from handleSendMessage)
  const handleKeywordDetected = (keyword: string) => {
    if (onKeywordDetected) {
      onKeywordDetected(keyword);
    }
  };

  // Handle clear reply
  const handleClearReply = () => {
    if (onClearReply) {
      onClearReply();
    }
  };

  // Xử lý sự kiện nhập liệu để phát hiện lệnh "/"
  const handleInputChange = (text: string) => {
    // Kiểm tra xem có phải là lệnh slash không
    if (text.startsWith('/')) {
      // Nếu chưa mở menu, mở menu lên
      if (!isMenuOpen) {
        setIsMenuOpen(true);
      }

      // Đánh dấu là đang trong chế độ slash command
      setIsSlashCommand(true);

      // Lấy phần text sau dấu "/"
      const searchText = text.substring(1);
      setFilterText(searchText);
    } else if (isSlashCommand) {
      // Nếu không còn bắt đầu bằng "/" nhưng trước đó là slash command
      setIsSlashCommand(false);
      setFilterText('');

      // Đóng menu nếu đang mở
      if (isMenuOpen) {
        setIsMenuOpen(false);
      }
    }
  };

  return (
    <div className="p-3 w-full">
      {chatStream?.isLoading && (
        <div className="w-full mb-3 flex items-center justify-center">
          <div className="w-full flex items-center space-x-2 px-4 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            {/* Loading animation với màu chủ đạo cho light mode */}
            <LoadingAnimation size="sm" speed={1.5} usePrimaryColor={true} />

            {/* Loading text */}
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {t('chat.aiThinking', 'AI đang suy nghĩ...')}
            </span>
          </div>
        </div>
      )}

      <div className="relative w-full" ref={chatInputRef}>
        {chatStream && addNotification && (
          <ChatInputBox
            onOpenMenu={() => setIsMenuOpen(true)}
            onInputChange={handleInputChange}
            onKeywordDetected={handleKeywordDetected}
            placeholder={t('chat.typeSlashForMenu', 'Nhập / để chọn nhanh menu...')}
            chatInputRef={textareaRef}
            chatStream={chatStream}
            addNotification={addNotification}
            replyMessage={replyMessage || null}
            onClearReply={handleClearReply}
            {...(setCenterNotification && { setCenterNotification })}
            editContent={editContent}
            onEditComplete={onEditComplete}
          />
        )}

        {/* Đặt menu ở ngoài để đảm bảo nó có width đúng */}
        {isMenuOpen && (
          <ModernMenu
            items={menuItems}
            isOpen={isMenuOpen}
            onClose={() => setIsMenuOpen(false)}
            chatInputWidth={chatInputWidth}
            filterText={isSlashCommand ? filterText : ''}
          />
        )}
      </div>
    </div>
  );
};

export default ChatInput;
